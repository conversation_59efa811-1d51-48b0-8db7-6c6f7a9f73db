using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UNI.Utilities.MinIo;
using Xunit;
using Xunit.Abstractions;

namespace UNI.Utilities.Test.MinIo
{
    public class MinIoServiceTests
    {
        private readonly Mock<IOptions<MinIoSettings>> _mockOptions;
        private readonly ILogger<MinIoService> _logger;
        private readonly MinIoSettings _settings;
        private readonly ITestOutputHelper _output;

        public MinIoServiceTests(ITestOutputHelper output)
        {
            _output = output;
            _settings = new MinIoSettings
            {
                Endpoint = "storage-dev.unicloudgroup.com.vn",
                AccessKey = "iZJWUfthMR4McKt6VvBO",
                SecretKey = "jUyIENNflJeHzMS4SH9qh5nWR7b6h47DMLaLiXcE",
                UseSSL = true,
                CreateBucketIfNotExists = true
            };

            _mockOptions = new Mock<IOptions<MinIoSettings>>();
            _mockOptions.Setup(x => x.Value).Returns(_settings);

            // Create a real logger that outputs to test output
            _logger = new TestLogger<MinIoService>(_output);
        }

        [Fact]
        public async Task UploadAndDownloadObject_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-bucket";
            var objectName = "test-file.txt";
            var content = "Hello, MinIO Test!";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            _output.WriteLine($"Starting test with bucket: {bucketName}, object: {objectName}");

            try
            {
                // Act - Upload
                _output.WriteLine("Starting upload operation...");
                using var stream = new MemoryStream(contentBytes);
                var uploadResult = await service.UploadObjectAsync(bucketName, objectName, stream);

                _output.WriteLine($"Upload completed. ETag: {uploadResult.ETag}, Size: {uploadResult.Size}");

                // Assert upload result
                Assert.NotNull(uploadResult);
                Assert.Equal(bucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(contentBytes.Length, uploadResult.Size);

                // Act - Download
                _output.WriteLine("Starting download operation...");
                var downloadedBytes = await service.GetObjectBytesAsync(bucketName, objectName);
                var downloadedContent = Encoding.UTF8.GetString(downloadedBytes);

                _output.WriteLine($"Download completed. Content length: {downloadedBytes.Length}");

                // Assert downloaded content
                Assert.Equal(content, downloadedContent);

                // Act - Check if exists
                _output.WriteLine("Checking if object exists...");
                var exists = await service.ObjectExistsAsync(bucketName, objectName);

                _output.WriteLine($"Object exists: {exists}");

                // Assert exists
                Assert.True(exists);

                // Act - Get object info
                _output.WriteLine("Getting object info...");
                var objectInfo = await service.GetObjectInfoAsync(bucketName, objectName);

                _output.WriteLine($"Object info retrieved. Size: {objectInfo.Size}, LastModified: {objectInfo.LastModified}");

                // Assert object info
                Assert.NotNull(objectInfo);
                Assert.Equal(objectName, objectInfo.ObjectName);
                Assert.Equal(contentBytes.Length, objectInfo.Size);
            }
            finally
            {
                // Cleanup
                _output.WriteLine("Starting cleanup...");
                await service.DeleteObjectAsync(bucketName, objectName);
                _output.WriteLine("Cleanup completed.");
            }
        }

        [Fact]
        public async Task BucketOperations_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-bucket-operations";

            try
            {
                // Act - Create bucket
                await service.CreateBucketAsync(bucketName);
                
                // Act - Check if exists
                var exists = await service.BucketExistsAsync(bucketName);
                
                // Assert exists
                Assert.True(exists);
                
                // Act - List buckets
                var buckets = await service.ListBucketsAsync();
                
                // Assert buckets
                Assert.Contains(buckets, b => b.Name == bucketName);
            }
            finally
            {
                // Cleanup
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task PresignedUrls_ShouldBeGenerated()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-presigned-urls";
            var objectName = "test-presigned.txt";
            var content = "Presigned URL Test";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                using var stream = new MemoryStream(contentBytes);
                await service.UploadObjectAsync(bucketName, objectName, stream);

                // Act - Generate URLs
                var downloadUrl = await service.GetPresignedDownloadUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var previewUrl = await service.GetPreviewUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var uploadUrl = await service.GetPresignedUploadUrlAsync(bucketName, "new-" + objectName, TimeSpan.FromMinutes(5));

                // Assert URLs
                Assert.NotNull(downloadUrl);
                Assert.NotNull(previewUrl);
                Assert.NotNull(uploadUrl);
                Assert.Contains(bucketName, downloadUrl);
                Assert.Contains(objectName, downloadUrl);
                Assert.Contains(bucketName, previewUrl);
                Assert.Contains(objectName, previewUrl);
                Assert.Contains(bucketName, uploadUrl);
                Assert.Contains("new-" + objectName, uploadUrl);
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task DownloadOperations_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-download";
            var objectName = "test-download.txt";
            var content = "Download Test Content";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                using var uploadStream = new MemoryStream(contentBytes);
                await service.UploadObjectAsync(bucketName, objectName, uploadStream);

                // Act - Download to stream
                using var downloadStream = new MemoryStream();
                await service.DownloadObjectAsync(bucketName, objectName, downloadStream);
                var downloadedContent = Encoding.UTF8.GetString(downloadStream.ToArray());

                // Assert stream download
                Assert.Equal(content, downloadedContent);

                // Act - Download to file
                var tempFilePath = Path.GetTempFileName();
                try
                {
                    await service.DownloadFileAsync(bucketName, objectName, tempFilePath);
                    var fileContent = await File.ReadAllTextAsync(tempFilePath);

                    // Assert file download
                    Assert.Equal(content, fileContent);
                }
                finally
                {
                    if (File.Exists(tempFilePath))
                        File.Delete(tempFilePath);
                }
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task LargeFileUpload_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-large-upload";
            var objectName = "large-test-file.bin";

            // Create a 10MB test file
            var largeFileSize = 10 * 1024 * 1024; // 10MB
            var largeFileData = new byte[largeFileSize];
            new Random().NextBytes(largeFileData);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Act - Upload large object from stream with progress tracking
                var progressReports = new List<UploadProgress>();
                var progress = new Progress<UploadProgress>(p => progressReports.Add(p));

                using var stream = new MemoryStream(largeFileData);
                var uploadResult = await service.UploadLargeObjectAsync(
                    bucketName,
                    objectName,
                    stream,
                    largeFileSize,
                    partSize: 5 * 1024 * 1024, // 5MB parts
                    progressCallback: progress);

                // Assert upload result
                Assert.NotNull(uploadResult);
                Assert.Equal(bucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(largeFileSize, uploadResult.Size);

                // Assert progress was reported
                Assert.NotEmpty(progressReports);
                Assert.True(progressReports.Any(p => p.PercentageComplete > 0));

                // Verify the uploaded file
                var exists = await service.ObjectExistsAsync(bucketName, objectName);
                Assert.True(exists);

                var objectInfo = await service.GetObjectInfoAsync(bucketName, objectName);
                Assert.Equal(largeFileSize, objectInfo.Size);
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task LargeFileUploadFromFile_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-large-file-upload";
            var objectName = "large-file-test.bin";

            // Create a temporary large file
            var tempFilePath = Path.GetTempFileName();
            var largeFileSize = 8 * 1024 * 1024; // 8MB
            var largeFileData = new byte[largeFileSize];
            new Random().NextBytes(largeFileData);

            try
            {
                await File.WriteAllBytesAsync(tempFilePath, largeFileData);

                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Act - Upload large file with progress tracking
                var progressReports = new List<UploadProgress>();
                var progress = new Progress<UploadProgress>(p => progressReports.Add(p));

                var uploadResult = await service.UploadLargeFileAsync(
                    bucketName,
                    objectName,
                    tempFilePath,
                    partSize: 5 * 1024 * 1024, // 5MB parts (minimum required)
                    progressCallback: progress);

                // Assert upload result
                Assert.NotNull(uploadResult);
                Assert.Equal(bucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(largeFileSize, uploadResult.Size);

                // Assert progress was reported
                Assert.NotEmpty(progressReports);

                // Verify the uploaded file
                var exists = await service.ObjectExistsAsync(bucketName, objectName);
                Assert.True(exists);
            }
            finally
            {
                // Cleanup
                if (File.Exists(tempFilePath))
                    File.Delete(tempFilePath);

                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task BatchDeleteObjects_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-batch-delete";
            var objectNames = new[] { "file1.txt", "file2.txt", "file3.txt" };
            var content = "Test content for batch delete";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Upload multiple objects
                foreach (var objectName in objectNames)
                {
                    using var stream = new MemoryStream(contentBytes);
                    await service.UploadObjectAsync(bucketName, objectName, stream);
                }

                // Verify all objects exist
                foreach (var objectName in objectNames)
                {
                    var exists = await service.ObjectExistsAsync(bucketName, objectName);
                    Assert.True(exists);
                }

                // Act - Batch delete
                var deleteResults = await service.DeleteObjectsAsync(bucketName, objectNames);

                // Assert delete results
                Assert.NotNull(deleteResults);
                Assert.Equal(objectNames.Length, deleteResults.Count());

                foreach (var result in deleteResults)
                {
                    Assert.True(result.IsSuccess);
                    Assert.Contains(result.ObjectName, objectNames);
                }

                // Verify all objects are deleted
                foreach (var objectName in objectNames)
                {
                    var exists = await service.ObjectExistsAsync(bucketName, objectName);
                    Assert.False(exists);
                }
            }
            finally
            {
                // Cleanup
                await service.DeleteBucketAsync(bucketName);
            }
        }

        [Fact]
        public async Task ListObjects_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = $"test-list-objects-{Guid.NewGuid():N}"; // Use unique bucket name
            var objectNames = new[] { "folder1/file1.txt", "folder1/file2.txt", "folder2/file3.txt", "root-file.txt" };
            var content = "Test content for listing";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup - Create bucket
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                // Clean up any existing objects first
                var existingObjects = await service.ListObjectsAsync(bucketName,null,true);
                if (existingObjects.Any())
                {
                    var existingObjectNames = existingObjects.Select(obj => obj.ObjectName).ToArray();
                    await service.DeleteObjectsAsync(bucketName, existingObjectNames);
                }

                // Upload multiple objects
                foreach (var objectName in objectNames)
                {
                    using var stream = new MemoryStream(contentBytes);
                    await service.UploadObjectAsync(bucketName, objectName, stream);

                    // Verify each upload immediately
                    var exists = await service.ObjectExistsAsync(bucketName, objectName);
                    Assert.True(exists, $"Object {objectName} was not uploaded successfully");
                }

                // Act - List all objects (recursive to get files in subdirectories)
                var allObjects = await service.ListObjectsAsync(bucketName, recursive: true);

                // Assert all objects
                Assert.NotNull(allObjects);
                // Debug: Log what objects were found
                var foundObjectNames = allObjects.Select(obj => obj.ObjectName).ToArray();
                var expectedObjectNames = string.Join(", ", objectNames);
                var actualObjectNames = string.Join(", ", foundObjectNames);

                Assert.True(allObjects.Count() == objectNames.Length,
                    $"Expected {objectNames.Length} objects but found {allObjects.Count()}. " +
                    $"Expected: [{expectedObjectNames}], " +
                    $"Actual: [{actualObjectNames}]");

                foreach (var objectName in objectNames)
                {
                    Assert.Contains(allObjects, obj => obj.ObjectName == objectName);
                }

                // Act - List objects with prefix
                var folder1Objects = await service.ListObjectsAsync(bucketName, "folder1/");

                // Assert filtered objects
                Assert.NotNull(folder1Objects);
                Assert.Equal(2, folder1Objects.Count());
                Assert.All(folder1Objects, obj => Assert.StartsWith("folder1/", obj.ObjectName));

                // Act - List objects recursively
                var recursiveObjects = await service.ListObjectsAsync(bucketName, recursive: true);

                // Assert recursive listing
                Assert.NotNull(recursiveObjects);
                Assert.Equal(objectNames.Length, recursiveObjects.Count());
            }
            finally
            {
                // Cleanup - Delete all objects in bucket
                try
                {
                    var allObjectsToDelete = await service.ListObjectsAsync(bucketName);
                    if (allObjectsToDelete.Any())
                    {
                        var objectNamesToDelete = allObjectsToDelete.Select(obj => obj.ObjectName).ToArray();
                        await service.DeleteObjectsAsync(bucketName, objectNamesToDelete);
                    }
                }
                catch
                {
                    // Ignore cleanup errors
                }

                try
                {
                    await service.DeleteBucketAsync(bucketName);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }

        [Fact]
        public async Task ProxyEndpointConfiguration_ShouldReplaceUrlsCorrectly()
        {
            // Arrange
            var proxySettings = new MinIoSettings
            {
                Endpoint = "storage-dev.unicloudgroup.com.vn",
                ProxyEndpoint = "https://dev-api-personal-bizzone.unicloudgroup.com.vn/Storage/GetFile",
                AccessKey = "iZJWUfthMR4McKt6VvBO",
                SecretKey = "jUyIENNflJeHzMS4SH9qh5nWR7b6h47DMLaLiXcE",
                UseSSL = true,
                CreateBucketIfNotExists = true
            };

            var mockProxyOptions = new Mock<IOptions<MinIoSettings>>();
            mockProxyOptions.Setup(x => x.Value).Returns(proxySettings);

            var service = new MinIoService(mockProxyOptions.Object, _logger);
            var bucketName = "test-proxy-endpoint";
            var objectName = "test-proxy.txt";
            var content = "Proxy endpoint test";
            var contentBytes = Encoding.UTF8.GetBytes(content);

            try
            {
                // Setup
                if (!await service.BucketExistsAsync(bucketName))
                {
                    await service.CreateBucketAsync(bucketName);
                }

                using var stream = new MemoryStream(contentBytes);
                await service.UploadObjectAsync(bucketName, objectName, stream);

                // Act - Generate URLs with proxy endpoint
                var downloadUrl = await service.GetPresignedDownloadUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var previewUrl = await service.GetPreviewUrlAsync(bucketName, objectName, TimeSpan.FromMinutes(5));
                var uploadUrl = await service.GetPresignedUploadUrlAsync(bucketName, "new-" + objectName, TimeSpan.FromMinutes(5));

                // Assert URLs use proxy endpoint
                Assert.NotNull(downloadUrl);
                Assert.NotNull(previewUrl);
                Assert.NotNull(uploadUrl);

                // URLs should contain the proxy endpoint host
                Assert.Contains("dev-api-personal-bizzone.unicloudgroup.com.vn", downloadUrl);
                Assert.Contains("dev-api-personal-bizzone.unicloudgroup.com.vn", previewUrl);
                Assert.Contains("dev-api-personal-bizzone.unicloudgroup.com.vn", uploadUrl);

                // URLs should not contain the original endpoint
                Assert.DoesNotContain("storage-dev.unicloudgroup.com.vn", downloadUrl);
                Assert.DoesNotContain("storage-dev.unicloudgroup.com.vn", previewUrl);
                Assert.DoesNotContain("storage-dev.unicloudgroup.com.vn", uploadUrl);
            }
            finally
            {
                // Cleanup
                await service.DeleteObjectAsync(bucketName, objectName);
                await service.DeleteBucketAsync(bucketName);
            }
        }

        #region Unit Tests (No MinIO Server Required)

        [Fact]
        public void MinIoService_Constructor_ShouldThrowNullReferenceException_WhenOptionsIsNull()
        {
            // Act & Assert
            Assert.Throws<NullReferenceException>(() => new MinIoService((IOptions<MinIoSettings>)null!, _logger));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldThrowArgumentNullException_WhenSettingsIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new MinIoService((MinIoSettings)null!, _logger));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldThrowArgumentNullException_WhenLoggerIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new MinIoService(_mockOptions.Object, null!));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldCreateInstance_WithValidParameters()
        {
            // Act
            var service = new MinIoService(_mockOptions.Object, _logger);

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public async Task UploadFileAsync_ShouldThrowMinIoException_WhenFileDoesNotExist()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var nonExistentFilePath = "non-existent-file.txt";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.UploadFileAsync("test-bucket", "test-object", nonExistentFilePath));

            Assert.Contains("File not found", exception.Message);
        }

        [Fact]
        public async Task UploadLargeFileAsync_ShouldThrowMinIoException_WhenFileDoesNotExist()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var nonExistentFilePath = "non-existent-large-file.txt";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.UploadLargeFileAsync("test-bucket", "test-object", nonExistentFilePath));

            Assert.Contains("File not found", exception.Message);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task BucketExistsAsync_ShouldThrowMinIoException_WhenBucketNameIsNullOrEmpty(string bucketName)
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() => service.BucketExistsAsync(bucketName));
            Assert.Contains("Bucket name cannot be empty", exception.Message);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task ObjectExistsAsync_ShouldThrowMinIoException_WhenBucketNameIsNullOrEmpty(string bucketName)
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() => service.ObjectExistsAsync(bucketName, "test-object"));
            Assert.Contains("Bucket name cannot be empty", exception.Message);
        }

        // Note: Null parameter validation tests are not included here because
        // the MinIO service validates bucket existence before parameter validation,
        // which requires a MinIO server connection. These would be integration tests.

        [Fact]
        public void MinIoService_ShouldImplementIDisposable()
        {
            // Arrange & Act
            var service = new MinIoService(_mockOptions.Object, _logger);

            // Assert
            Assert.IsAssignableFrom<IDisposable>(service);

            // Act - Should not throw
            service.Dispose();
        }

        [Fact]
        public void MinIoService_Dispose_ShouldBeCallableMultipleTimes()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);

            // Act & Assert - Should not throw
            service.Dispose();
            service.Dispose();
            service.Dispose();
        }

        [Theory]
        [InlineData("", "access-key", "secret-key")]
        [InlineData(null, "access-key", "secret-key")]
        [InlineData("localhost:9000", "", "secret-key")]
        [InlineData("localhost:9000", null, "secret-key")]
        [InlineData("localhost:9000", "access-key", "")]
        [InlineData("localhost:9000", "access-key", null)]
        public void MinIoService_Constructor_ShouldThrowArgumentException_WhenSettingsAreInvalid(
            string endpoint, string accessKey, string secretKey)
        {
            // Arrange
            var invalidSettings = new MinIoSettings
            {
                Endpoint = endpoint,
                AccessKey = accessKey,
                SecretKey = secretKey,
                UseSSL = false,
                CreateBucketIfNotExists = true
            };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => new MinIoService(invalidSettings, _logger));
        }

        [Fact]
        public void MinIoService_Constructor_ShouldCreateInstance_WithValidSettings()
        {
            // Arrange
            var validSettings = new MinIoSettings
            {
                Endpoint = "localhost:9000",
                AccessKey = "test-access-key",
                SecretKey = "test-secret-key",
                UseSSL = false,
                CreateBucketIfNotExists = true
            };

            // Act
            var service = new MinIoService(validSettings, _logger);

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public async Task DeleteObjectsAsync_ShouldThrowMinIoException_WhenObjectNamesIsNull()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.DeleteObjectsAsync("test-bucket", null!));
            Assert.Contains("Value cannot be null", exception.Message);
        }

        [Fact]
        public async Task DeleteObjectsAsync_ShouldThrowMinIoException_WhenObjectNamesIsEmpty()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var emptyObjectNames = new string[0];

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.DeleteObjectsAsync("test-bucket", emptyObjectNames));
            Assert.Contains("Please assign list of object names", exception.Message);
        }

        [Theory]
        [InlineData(4 * 1024 * 1024)] // 4MB - below minimum
        [InlineData(6L * 1024 * 1024 * 1024)] // 6GB - above maximum
        public async Task UploadLargeObjectAsync_ShouldThrowMinIoException_WhenPartSizeIsInvalid(long partSize)
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            using var stream = new MemoryStream(new byte[1024]);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<MinIoException>(() =>
                service.UploadLargeObjectAsync("test-bucket", "test-object", stream, 1024, partSize: partSize));
            Assert.Contains("Part size", exception.Message);
        }

        #endregion

        #region Helper Methods for Testing

        private static byte[] GenerateRandomBytes(int size)
        {
            var random = new Random();
            var bytes = new byte[size];
            random.NextBytes(bytes);
            return bytes;
        }

        private static string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        [Fact(
            // Skip = "Integration test - requires MinIO server and significant disk space"
        )]
        public async Task UploadLargeFile_GreaterThan5GB_ShouldWork()
        {
            // Arrange
            var service = new MinIoService(_mockOptions.Object, _logger);
            var bucketName = "test-large-upload";
            var objectName = "large-test-file.bin";

            // Create a temporary file path
            var tempFilePath = Path.Combine(Path.GetTempPath(), $"minio-test-{Guid.NewGuid()}.tmp");

            try
            {
                // Create a sparse file of 5.5GB (to exceed the 5GB threshold) - 5,500,000,000 bytes
                long fileSize = (long)(0.1 * 1024 * 1024 * 1024); // 5.5GB (5,500,000,000 bytes)

                _logger.LogInformation("Creating sparse test file of {Size:N0} bytes at {Path}", fileSize, tempFilePath);

                using (var fileStream = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write))
                {
                    // Create a sparse file by setting the length
                    fileStream.SetLength(fileSize);

                    // Write some data at the beginning
                    var startData = Encoding.UTF8.GetBytes("Start of large file");
                    fileStream.Write(startData, 0, startData.Length);

                    // Write some data at the end to ensure the file is properly created
                    fileStream.Seek(fileSize - 100, SeekOrigin.Begin);
                    var endData = Encoding.UTF8.GetBytes("End of large file");
                    fileStream.Write(endData, 0, endData.Length);
                }

                // Verify the file was created with the correct size
                var fileInfo = new FileInfo(tempFilePath);
                Assert.Equal(fileSize, fileInfo.Length);
                _logger.LogInformation("Test file created successfully with size: {Size:N0} bytes", fileInfo.Length);
                
                // Setup progress tracking with detailed logging
                var progressReports = new List<UploadProgress>();
                var lastPercentage = 0;
                var startTime = DateTime.UtcNow;
                
                var progress = new Progress<UploadProgress>(p => 
                {
                    progressReports.Add(p);
                    
                    // Log progress at reasonable intervals to avoid flooding the output
                    var currentPercentage = (int)p.PercentageComplete;
                    if (currentPercentage > lastPercentage || p.Status == "Upload completed")
                    {
                        lastPercentage = currentPercentage;

                        var logMessage = $"Upload Progress: {p.PercentageComplete:F1}% ({p.UploadedBytes:N0}/{p.TotalBytes:N0} bytes)";

                        if (p.BytesPerSecond.HasValue)
                        {
                            var mbps = p.BytesPerSecond.Value / (1024.0 * 1024.0);
                            logMessage += $" - Speed: {mbps:F2} MB/s";
                        }

                        if (p.EstimatedTimeRemaining.HasValue)
                        {
                            logMessage += $" - ETA: {p.EstimatedTimeRemaining.Value:hh\\:mm\\:ss}";
                        }

                        logMessage += $" - Status: {p.Status}";

                        _logger.LogInformation(logMessage);
                        _output.WriteLine(logMessage);
                    }
                });

                _logger.LogInformation("Starting large file upload with part size: {PartSize:N0} bytes", 100 * 1024 * 1024);

                // Act - Upload the large file
                var uploadResult = await service.UploadLargeFileAsync(
                    bucketName,
                    objectName,
                    tempFilePath,
                    contentType: "application/octet-stream",
                    partSize: 10 * 1024 * 1024, // 10MB parts
                    progressCallback: progress);

                var totalTime = DateTime.UtcNow - startTime;
                var averageSpeed = fileSize / totalTime.TotalSeconds / (1024 * 1024);

                _logger.LogInformation("Upload completed in {Time:hh\\:mm\\:ss} at average speed of {Speed:F2} MB/s",
                    totalTime, averageSpeed);

                // Assert upload result
                Assert.NotNull(uploadResult);
                Assert.Equal(bucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(fileSize, uploadResult.Size);
                Assert.True(uploadResult.IsMultipartUpload);

                // Assert progress was reported
                Assert.NotEmpty(progressReports);
                Assert.True(progressReports.Any(p => p.PercentageComplete > 0));
                Assert.True(progressReports.Any(p => p.BytesPerSecond.HasValue));

                // Log progress statistics
                var maxSpeed = progressReports.Max(p => p.BytesPerSecond ?? 0) / (1024.0 * 1024.0);
                var avgSpeed = progressReports.Where(p => p.BytesPerSecond.HasValue).Average(p => p.BytesPerSecond!.Value) / (1024.0 * 1024.0);

                _logger.LogInformation("Progress statistics: Max speed: {MaxSpeed:F2} MB/s, Avg speed: {AvgSpeed:F2} MB/s, " +
                    "Progress updates: {Updates}", maxSpeed, avgSpeed, progressReports.Count);

                // Verify the uploaded file exists
                _logger.LogInformation("Verifying uploaded file...");
                var exists = await service.ObjectExistsAsync(bucketName, objectName);
                Assert.True(exists);

                // Verify the object info
                var objectInfo = await service.GetObjectInfoAsync(bucketName, objectName);
                Assert.Equal(fileSize, objectInfo.Size);
                _logger.LogInformation("Verification successful. Remote object size: {Size:N0} bytes", objectInfo.Size);
            }
            finally
            {
                // Cleanup the local file
                if (File.Exists(tempFilePath))
                {
                    _logger.LogInformation("Cleaning up local test file...");
                    File.Delete(tempFilePath);
                }

                // Cleanup the remote object
                try
                {
                    _logger.LogInformation("Cleaning up remote test objects...");
                    await service.DeleteObjectAsync(bucketName, objectName);
                    await service.DeleteBucketAsync(bucketName);
                }
                catch (Exception ex)
                {
                    // Log cleanup errors but don't fail the test
                    _logger.LogWarning(ex, "Error during cleanup");
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// Simple test logger that outputs to xUnit test output
    /// </summary>
    public class TestLogger<T> : ILogger<T>
    {
        private readonly ITestOutputHelper _output;

        public TestLogger(ITestOutputHelper output)
        {
            _output = output;
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull
        {
            return null;
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return true;
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            var message = formatter(state, exception);
            var logMessage = $"[{logLevel}] {typeof(T).Name}: {message}";

            if (exception != null)
            {
                logMessage += $"\nException: {exception}";
            }

            _output.WriteLine(logMessage);
        }
    }
}



